%load_ext autoreload
%autoreload 2

# pip install sqlalchemy
# !pip install python-slugify
# !pip install pytz
# !pip install pandas

# SETTINGS
from troli.settings import MAIN_DB_PATH
print(f'DATABASE_PATH: {MAIN_DB_PATH}')

from typing import List, Dict, Any, Optional, Tuple

import pandas as pd
import json
from datetime import datetime

from troli.models import Retailer, Store, Sitemap, ScrapedData, ScrapeType, Product, StoreProduct, PriceHistory, StockHistory

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from sqlalchemy.orm import joinedload

from sqlalchemy.exc import IntegrityError


# Database connection
# engine = create_engine("postgresql://postgres:12345678@localhost:5432/Troli-Scraper-V2-test")
engine = create_engine(MAIN_DB_PATH) #, echo=True)
Session = sessionmaker(bind=engine)


# creating or updating the following tables with scraped data: Product, StoreProduct, PriceHistory, StockHistory
print(f"ScrapedData:{ScrapedData.__table__.columns.keys()}")
print(f"Product:{Product.__table__.columns.keys()}")
print(f"StoreProduct:{StoreProduct.__table__.columns.keys()}")
print(f"PriceHistory:{PriceHistory.__table__.columns.keys()}")
print(f"StockHistory:{StockHistory.__table__.columns.keys()}")

# OBJECTIVE: creating or updating the following tables with the cleaned output of the scraped data: Product, StoreProduct, PriceHistory, StockHistory


# SCHEMA:
# Product:['id', 'sku', 'barcode', 'name', 'slug', 'url', 'unit_of_measurement', 'quantity_of_unit', 'is_liquor', 'date_scraped', 'date_created', 'date_updated', 'thumbnail_image_url', 'detail_image_url', 'description', 'retailer_id']
# StoreProduct:['id', 'store_id', 'product_id', 'price', 'is_priced_per_unit', 'price_per_unit', 'dt_price_scraped_at', 'is_in_stock', 'stock_count', 'dt_stock_count_scraped_at', 'is_on_promotion', 'dt_created', 'dt_updated']
# PriceHistory:['id', 'store_product_id', 'price', 'price_per_unit', 'date_scraped']
# StockHistory:['id', 'store_product_id', 'stock_status', 'stock_count', 'date_scraped']


# The following is how we will group the raw data and supply it to the tables. will create product first, then storeproduct, then pricehistory, then stockhistory. will deal with similar data together, eg all price variables will be worked together so as to add some structure to the code.

# _______________________
# Product Table groupings:

# info:
# 'id', 'sku', 'barcode', 'name', 'slug', 'url', 'unit_of_measurement', 'quantity_of_unit', 'is_liquor', 'description',

# metadata:
# 'date_scraped', 'date_created', 'date_updated', 

# relationships:
# 'store_products', 'retailer_id'
# _______________________

# _______________________
# StoreProduct Table groupings:

# prices:
# 'price', 'is_priced_per_unit', 'price_per_unit', 

# stock count:
# 'is_in_stock', 'stock_count',

# promos:
# 'is_on_promotion',
# metadata:
# 'dt_price_scraped_at', 'dt_stock_count_scraped_at', 'dt_created', 'dt_updated'

# relationships:
# 'store_id', 'product_id'
# _______________________

# _______________________
# PriceHistory Table groupings:

# prices:
# 'price', 'price_per_unit',
# relationships:
# 'store_product_id'
# metadata:
# 'date_scraped'
# _______________________

# _______________________
# StockHistory Table groupings:

# stock count:
# 'stock_status', 'stock_count',
# relationships:
# 'store_product_id'
# metadata:
# 'date_scraped'
# _______________________

PNP_LIQUOR_IDENTIFYING_CATEGORY = "Liquor Store"
MAIN_RETAILER_URL = 'https://www.pnp.co.za'

# FUNCTIONS
def read_uncleaned_scraped_data(
        session: sessionmaker, 
        retailer: Retailer, 
        scrape_type: str = ScrapeType.CATEGORY.value            
        ) -> ScrapedData:
    """
    Create or update a product.
    Args:
        session (Session): The SQLAlchemy session.
        retailer (Retailer): The retailer of the product.
    Returns:
        ScrapedData: returns a ScrapedData object.
        e.g "<ScrapedData(retailer=Pick n Pay, store=PnP Hyper Somerset Mall, scrape_type=category, dt_scraped=2024-06-21 15:43:40)>"

    """
    uncleaned_scraped_data = session.query(ScrapedData).filter(
        ScrapedData.retailer_id == retailer.id,
        ScrapedData.is_cleaned == False,
        ScrapedData.scrape_type == scrape_type
    ).options(joinedload(ScrapedData.store)).first()
    
    return uncleaned_scraped_data


def pnp_map_scraped_data_to_product(
        raw_product_json: Dict,
        uncleaned_scraped_data: ScrapedData,
        PNP_LIQUOR_IDENTIFYING_CATEGORY: str
        ) -> Dict:
    """
    Maps raw scraped JSON data to the columns of a Product table in a structured format.
    
    Parameters:
        raw_product_json (Dict): The raw JSON data containing product information. A single entry in ScrapedData.data.
        uncleaned_scraped_data (ScrapedData ): A ScrapedData object.
        PNP_LIQUOR_IDENTIFYING_CATEGORY (str): String identifier used to determine 
                                               if a product belongs to the liquor category.
    
    Returns:
        Dict: A dictionary containing the cleaned product data, structured for the Product table.
              Keys correspond to column names in the Product table, values are derived from 
              the raw JSON and metadata.
    
    Columns Outputted:
        - id: None (not provided by scraped data; managed by the database)
        - sku: Product identifier from raw JSON `code`.
        - barcode: None (not currently mapped but could be derived in future).
        - name: Product name from raw JSON `name`.
        - slug: None (could be generated if required).
        - url: Constructed URL based on the product `code`.
        - unit_of_measurement: None (not currently mapped).
        - quantity_of_unit: None (not currently mapped).
        - is_liquor: Boolean flag derived from `categoryNames` in raw JSON.
        - date_scraped: Scrape timestamp from `uncleaned_scraped_data.dt_scraped`.
        - date_created: None (could be auto-generated by the database).
        - date_updated: None (could be auto-generated by the database).
        - thumbnail_image_url: URL from `images` where format is `thumbnail`.
        - detail_image_url: URL from `images` where format is `product`.
        - description: None (placeholder for potential mapping).
        - retailer_id: Retailer identifier from `uncleaned_scraped_data.retailer`.
    """
    # Initialize product dictionary with default None values
    product_data = {
        "id": None,
        "sku": None,
        "barcode": None,
        "name": None,
        "slug": None,
        "url": None,
        "unit_of_measurement": None,
        "quantity_of_unit": None,
        "is_liquor": None,
        "date_scraped": None,
        "date_created": None,
        "date_updated": None,
        "thumbnail_image_url": None,
        "detail_image_url": None,
        "description": None,
        "retailer_id": None,
    }
    
    # Map `sku` from `code`
    if "code" in raw_product_json:
        product_data["sku"] = raw_product_json["code"]
    
    # Map `name`
    if "name" in raw_product_json:
        product_data["name"] = raw_product_json["name"]
    
    # Construct `url`
    if product_data["sku"]:
        product_data["url"] = f'https://www.pnp.co.za/p/{product_data["sku"]}'
    
    # Map `is_liquor` based on `categoryNames`
    if "categoryNames" in raw_product_json:
        product_data["is_liquor"] = PNP_LIQUOR_IDENTIFYING_CATEGORY in raw_product_json["categoryNames"]
    
    # Map `date_scraped` from uncleaned product data
    product_data["date_scraped"] = getattr(uncleaned_scraped_data, "dt_scraped", None)
    if not product_data["date_scraped"]:
        raise ValueError("date_scraped not found in uncleaned_scraped_data")
    
    # Map image URLs
    if "images" in raw_product_json:
        for image in raw_product_json["images"]:
            if image.get("format") == "thumbnail":
                product_data["thumbnail_image_url"] = image.get("url")
            elif image.get("format") == "product":
                product_data["detail_image_url"] = image.get("url")
    
    # Map `retailer_id`
    product_data["retailer_id"] = getattr(uncleaned_scraped_data, "retailer", None)
    
    return product_data


def create_or_update_product(
        session: sessionmaker, 
        sku: str, 
        name: str, 
        url: str, 
        is_liquor: bool, 
        date_scraped: datetime, 
        thumbnail_image_url: str, 
        detail_image_url: str, 
        description: str, 
        retailer: Retailer
        
        # functions to update the product
        
        ) -> Tuple[bool, Product]:
    """
    Create or update a product.
    Args:
        session (Session): The SQLAlchemy session.
        sku (str): The SKU of the product.
        name (str): The name of the product.
        url (str): The URL of the product.
        is_liquor (bool): Whether the product is a liquor.
        date_scraped (datetime): The date the product was scraped.
        thumbnail_image_url (str): The URL of the thumbnail image.
        detail_image_url (str): The URL of the detail image.
        description (str): The description of the product.
        retailer (Retailer): The retailer of the product.
    Returns:
        Product: The updated or created product.
        e.g "(True, <Product(name=Huggies Gold Size 4 (8-14kg) Value Pack 50 Nappies, sku=000000000000772237_EA)>)"
    """
    product = session.query(Product).filter_by(sku=sku, retailer=retailer).first()
    created = False
    if product is None:
        print(f'Creating product with sku: {sku}')
        created = True
        product = Product(
            sku=sku,
            name=name,
            url=url,
            is_liquor=is_liquor,
            date_scraped=date_scraped,
            thumbnail_image_url=thumbnail_image_url,
            detail_image_url=detail_image_url,
            description=description,
            retailer=retailer
        )
        session.add(product)
    else:
        print(f'Updating product with sku: {sku}???')
        product.name = name
        product.url = url
        product.is_liquor = is_liquor
        product.date_scraped = date_scraped
        product.thumbnail_image_url = thumbnail_image_url
        product.detail_image_url = detail_image_url
        product.description = description
        product.retailer = retailer
    session.commit()
    return created, product


def pnp_map_scraped_data_to_store_product(
        raw_product_json: Dict,
        uncleaned_scraped_data: ScrapedData, 
        current_store:Store
        ) -> Dict:
    """
    Maps raw scraped JSON data to the columns of a StoreProduct table in a structured format.

    Parameters:
        raw_product_json (Dict): The raw JSON data containing product and store information.
        uncleaned_scraped_data (object): An object containing additional metadata 
                                          (e.g., `dt_scraped` for scraping timestamps).
        current_store (Store): Store table object.

    Returns:
        Dict: A dictionary containing the cleaned store-product data, structured for the StoreProduct table.
              Keys correspond to column names in the StoreProduct table, values are derived from 
              the raw JSON and metadata.

    Columns Outputted:
        - id: None (managed by the database)
        - store_id: None (placeholder for store ID from external data)
        - product_id: None (placeholder for product ID from external data)
        - price: Product price from `price.value` in raw JSON.
        - is_priced_per_unit: None (not currently mapped).
        - price_per_unit: None (not currently mapped).
        - dt_price_scraped_at: Timestamp when price was scraped from `uncleaned_scraped_data.dt_scraped`.
        - is_in_stock: Boolean flag based on `stock.stockLevelStatus` in raw JSON.
        - dt_is_in_stock_scraped_at: Timestamp from `uncleaned_scraped_data.dt_scraped`.
        - stock_count: None (not currently mapped).
        - dt_stock_count_scraped_at: Timestamp from `uncleaned_scraped_data.dt_scraped`.
        - is_on_promotion: Boolean flag based on the presence of `potentialPromotions` in raw JSON.
        - dt_created: None (placeholder for database-generated timestamp).
        - dt_updated: None (placeholder for database-generated timestamp).
    """
    # Initialize store-product dictionary with default None values
    store_product_data = {
        "id": None,
        "store_id": None,
        "product_id": None,
        "scrape_type": None,
        "price": None,
        "is_priced_per_unit": None,
        "price_per_unit": None,
        "dt_price_scraped_at": None,
        "is_in_stock": None,
        "dt_is_in_stock_scraped_at": None,
        "stock_count": None,
        "dt_stock_count_scraped_at": None,
        "is_on_promotion": 0,
        "dt_created": None,
        "dt_updated": None,
    }

    # Store
    store_id = current_store.id
    store_product_data['store_id'] = store_id

    # scrape_type
    store_product_data['scrape_type'] = uncleaned_scraped_data.scrape_type

    # Map `price` from `price.value`
    if "price" in raw_product_json and "value" in raw_product_json["price"]:
        store_product_data["price"] = raw_product_json["price"]["value"]

    # Map `dt_price_scraped_at` from uncleaned product data
    store_product_data["dt_price_scraped_at"] = getattr(uncleaned_scraped_data, "dt_scraped", None)
    if not store_product_data["dt_price_scraped_at"]:
        raise ValueError("dt_scraped not found in uncleaned_scraped_data")

    # Map `is_in_stock` from `stock.stockLevelStatus`
    if "stock" in raw_product_json and "stockLevelStatus" in raw_product_json["stock"]:
        store_product_data["is_in_stock"] = raw_product_json["stock"]["stockLevelStatus"] == "inStock"

    # Map `dt_is_in_stock_scraped_at` (same as `dt_price_scraped_at`)
    if uncleaned_scraped_data.scrape_type == ScrapeType.CATEGORY.value:
        store_product_data["dt_is_in_stock_scraped_at"] = getattr(uncleaned_scraped_data, "dt_scraped", None)

    # Map `dt_stock_count_scraped_at` (same as `dt_price_scraped_at`)
    # store_product_data["dt_stock_count_scraped_at"] = getattr(uncleaned_scraped_data, "dt_scraped", None)

    # Map `is_on_promotion` from `potentialPromotions`
    if "potentialPromotions" in raw_product_json:
        store_product_data["is_on_promotion"] = len(raw_product_json["potentialPromotions"]) > 0

    return store_product_data


def create_or_update_store_product(
    session: sessionmaker, 
    product: Product, 
    store: Store, 
    scrape_type: ScrapeType,
    price: float, 
    is_priced_per_unit: Optional[bool], 
    price_per_unit: Optional[float], 
    dt_price_scraped_at: datetime, 
    is_in_stock: bool,
    dt_is_in_stock_scraped_at:datetime,
    stock_count: Optional[int], 
    dt_stock_count_scraped_at: datetime, 
    is_on_promotion: bool, 
    ) -> Tuple[bool, StoreProduct]:
    """
    Create or update a store product.
    Args:
        session (Session): The SQLAlchemy session.
        product (Product): The product.
        store (Store): The store.
        scrape_type (ScrapeType): The type of scrape.
        price (float): The price of the product.
        is_priced_per_unit (bool): Whether the product is priced per unit.
        price_per_unit (float): The price per unit of the product.
        dt_price_scraped_at (datetime): The date the price was scraped.
        is_in_stock (bool): Whether the product is in stock.
        dt_is_in_stock_scraped_at (datetime): The date that is_in_stock was scraped.
        stock_count (int): The stock count of the product.
        dt_stock_count_scraped_at (datetime): The date the stock count was scraped.
        is_on_promotion (bool): Whether the product is on promotion.
    Returns:
        StoreProduct: The updated or created store product.
    """
    store_product = session.query(StoreProduct).filter_by(product=product, store=store).first()
    created = False
    if store_product is None:
        print(f'Creating store product with product: {product.id} and store: {store}')
        created = True
        store_product = StoreProduct(
            product=product,
            store=store,
            price=price,
            is_priced_per_unit=is_priced_per_unit,
            price_per_unit=price_per_unit,
            dt_price_scraped_at=dt_price_scraped_at,
            is_in_stock=is_in_stock,
            dt_is_in_stock_scraped_at=dt_is_in_stock_scraped_at,
            stock_count=stock_count,
            dt_stock_count_scraped_at=dt_stock_count_scraped_at,
            is_on_promotion=is_on_promotion
        )
        session.add(store_product)
    else:
        print(f'Updating store product with product: {product.id} and store: {store}')
        if scrape_type == ScrapeType.CATEGORY.value:
            store_product.price = price
            store_product.dt_price_scraped_at = dt_price_scraped_at
            store_product.is_in_stock = is_in_stock
            store_product.dt_stock_count_scraped_at = dt_stock_count_scraped_at
            store_product.is_on_promotion = is_on_promotion
            store_product.dt_updated = datetime.now()
        elif scrape_type == ScrapeType.DETAILS.value:
            store_product.price = price
            store_product.is_priced_per_unit = is_priced_per_unit
            store_product.price_per_unit = price_per_unit
            store_product.dt_price_scraped_at = dt_price_scraped_at
            store_product.is_in_stock = is_in_stock
            store_product.stock_count = stock_count
            store_product.dt_stock_count_scraped_at = dt_stock_count_scraped_at
            store_product.is_on_promotion = is_on_promotion
            store_product.dt_updated = datetime.now()
    session.commit()
    return created, store_product


def create_or_update_price_history(
    session: sessionmaker, 
    store_product: StoreProduct, 
    price: float, 
    price_per_unit: Optional[float], 
    dt_price_scraped_at: datetime
    ) -> PriceHistory:
    """
    Create or update a price history.
    Args:
        session (Session): The SQLAlchemy session.
        store_product (StoreProduct): The store product.
        price (float): The price of the product.
        price_per_unit (float): The price per unit of the product.
        dt_price_scraped_at (datetime): The date the price was scraped.
    Returns:
        PriceHistory: The updated or created price history.
    """
    price_history = session.query(PriceHistory).filter_by(
        store_product=store_product,
        price=price,
        price_per_unit=price_per_unit,
        dt_price_scraped_at=dt_price_scraped_at
        ).first()
    if price_history is None:
        price_history = PriceHistory(
            store_product=store_product,
            price=price,
            price_per_unit=price_per_unit,
            dt_price_scraped_at=dt_price_scraped_at
        )
        session.add(price_history)
        session.commit()
    else:
        print(f'Price history already exists for store product: {store_product.id}')
    return price_history


def create_or_update_stock_history(
    session: sessionmaker,
    store_product: StoreProduct,
    is_in_stock: bool,
    dt_is_in_stock_scraped_at: datetime,
    stock_count: Optional[int],
    dt_stock_count_scraped_at: datetime
    ) -> StockHistory:
    """
    Create or update a stock history.
    Args:
        session (Session): The SQLAlchemy session.
        store_product (StoreProduct): The store product.
        is_in_stock (bool): Whether the product is in stock.
        dt_is_in_stock_scraped_at (datetime): The date that is_in_stock was scraped.
        stock_count (int): The stock count of the product.
        dt_stock_count_scraped_at (datetime): The date the stock count was scraped.
    Returns:
        StockHistory: The updated or created stock history.
    """
    stock_history = session.query(StockHistory).filter_by(
        store_product=store_product,
        is_in_stock=is_in_stock,
        dt_is_in_stock_scraped_at=dt_is_in_stock_scraped_at,
        stock_count=stock_count,
        dt_stock_count_scraped_at=dt_stock_count_scraped_at
        ).first()
    if stock_history is None:
        stock_history = StockHistory(
            store_product=store_product,
            is_in_stock=is_in_stock,
            dt_is_in_stock_scraped_at=dt_is_in_stock_scraped_at,
            stock_count=stock_count,
            dt_stock_count_scraped_at=dt_stock_count_scraped_at
        )
        session.add(stock_history)
        session.commit()
    else:
        print(f'Stock history already exists for store product: {store_product.id}')
    return stock_history


def clean_and_insert_raw_scraped_data_to_db(
    session: sessionmaker,
    MAIN_RETAILER_URL: str,
    PNP_LIQUOR_IDENTIFYING_CATEGORY: str,

    # functions
    read_uncleaned_scraped_data: callable,
    pnp_map_scraped_data_to_product: callable,
    create_or_update_product: callable,
    pnp_map_scraped_data_to_store_product: callable,
    create_or_update_store_product: callable,
    create_or_update_price_history: callable,
    create_or_update_stock_history: callable,
    ):
    """
    Cleans and inserts scraped data into the database.
    Args:
        session (Session): The SQLAlchemy session.
        MAIN_RETAILER_URL (str): The main retailer URL.
        PNP_LIQUOR_IDENTIFYING_CATEGORY (str): String identifier used to determine if a product belongs to the liquor category.
        
    """
    global uncleaned_scraped_data_data
    retailer = session.query(Retailer).filter_by(url=MAIN_RETAILER_URL).first()
    
    # <ScrapedData>
    uncleaned_scraped_data = read_uncleaned_scraped_data(session=session, retailer=retailer)
    print(len(uncleaned_scraped_data.data))
    uncleaned_scraped_data_data = uncleaned_scraped_data.data
    raw_products_on_page_json:dict = json.loads(uncleaned_scraped_data_data)

    # <Store>
    current_store:Store = uncleaned_scraped_data.store
    # print(f"len(raw_products_on_page_json): '{len(raw_products_on_page_json)}")
    for raw_product_json in raw_products_on_page_json:
        
        # <Product>
        mapped_product_data = pnp_map_scraped_data_to_product(raw_product_json=raw_product_json, uncleaned_scraped_data=uncleaned_scraped_data,PNP_LIQUOR_IDENTIFYING_CATEGORY=PNP_LIQUOR_IDENTIFYING_CATEGORY)
        # print(f"mapped_product_data: {mapped_product_data}")
        was_created, cleaned_product = create_or_update_product(
            session=session,
            sku = mapped_product_data["sku"],
            name = mapped_product_data["name"],
            url = mapped_product_data["url"],
            is_liquor = mapped_product_data["is_liquor"],
            date_scraped = mapped_product_data["date_scraped"],
            thumbnail_image_url = mapped_product_data["thumbnail_image_url"],
            detail_image_url = mapped_product_data["detail_image_url"],
            description = mapped_product_data["description"],
            retailer = mapped_product_data["retailer_id"],
        )
        # print(f"create_or_update_product: {was_created, cleaned_product}")

        # <StoreProduct>
        mapped_store_product_data = pnp_map_scraped_data_to_store_product(raw_product_json=raw_product_json, uncleaned_scraped_data=uncleaned_scraped_data,current_store=current_store)
        print(f"mapped_store_product_data: {mapped_store_product_data}")
        was_created, cleaned_store_product = create_or_update_store_product(
            session=session,
            product=cleaned_product,
            store=current_store,
            scrape_type=mapped_store_product_data['scrape_type'],
            price=mapped_store_product_data['price'],
            is_priced_per_unit=mapped_store_product_data['is_priced_per_unit'],
            price_per_unit=mapped_store_product_data['price_per_unit'],
            dt_price_scraped_at=mapped_store_product_data['dt_price_scraped_at'],
            is_in_stock=mapped_store_product_data['is_in_stock'],
            dt_is_in_stock_scraped_at=mapped_store_product_data['dt_is_in_stock_scraped_at'],
            stock_count=mapped_store_product_data['stock_count'],
            dt_stock_count_scraped_at=mapped_store_product_data['dt_stock_count_scraped_at'],
            is_on_promotion=mapped_store_product_data['is_on_promotion'],
        )
        print(f"create_or_update_store_product: {was_created, cleaned_store_product}")

        # <PriceHistory>
        # use the `mapped_store_product_data` data to create a price history
        price_history = create_or_update_price_history(
            session=session,
            store_product=cleaned_store_product,
            price=mapped_store_product_data['price'],
            price_per_unit=mapped_store_product_data['price_per_unit'],
            dt_price_scraped_at=mapped_store_product_data['dt_price_scraped_at']
        )
        print(f"create_or_update_price_history: {price_history}")

        # <StockHistory>
        # use the `mapped_store_product_data` data to create a stock history
        stock_history = create_or_update_stock_history(
            session=session,
            store_product=cleaned_store_product,
            is_in_stock=mapped_store_product_data['is_in_stock'],
            dt_is_in_stock_scraped_at=mapped_store_product_data['dt_is_in_stock_scraped_at'],
            stock_count=mapped_store_product_data['stock_count'],
            dt_stock_count_scraped_at=mapped_store_product_data['dt_stock_count_scraped_at']
        )
        print(f"create_or_update_stock_history: {stock_history}")

        print(f"{'-'*50}")

    # write to ScrapedData that the data has been cleaned
    uncleaned_scraped_data.is_cleaned = True
    uncleaned_scraped_data.dt_updated = datetime.now()
    session.commit()
    print(f"{'*'*100}")

 

#  if there is uncleaned data, clean it
i = 0
while i<1000:
    with Session() as session:
        uncleaned_count = session.query(ScrapedData).filter(ScrapedData.is_cleaned == False).count()
        print(f"uncleaned_count: {uncleaned_count}")

        # every 100 iterations, print the count of uncleaned data
        if uncleaned_count % 100 == 0:
            print(f"uncleaned_count: {uncleaned_count}")
        if uncleaned_count == 0:
            break


        try:
            clean_and_insert_raw_scraped_data_to_db(
                session=session,
                MAIN_RETAILER_URL=MAIN_RETAILER_URL,
                PNP_LIQUOR_IDENTIFYING_CATEGORY=PNP_LIQUOR_IDENTIFYING_CATEGORY,
                read_uncleaned_scraped_data=read_uncleaned_scraped_data,
                pnp_map_scraped_data_to_product=pnp_map_scraped_data_to_product,
                create_or_update_product=create_or_update_product,
                pnp_map_scraped_data_to_store_product=pnp_map_scraped_data_to_store_product,
                create_or_update_store_product=create_or_update_store_product,
                create_or_update_price_history=create_or_update_price_history,
                create_or_update_stock_history=create_or_update_stock_history,
            )
        except Exception as e:
            print(f"Error: {e}")
            session.rollback()
            raise e

        i+=1



uncleaned_scraped_data_data


#  READ INSERTED DATA
# The following functions are needed:
# get latest store_product entry for a given product Product sku
# get historic prices for the given product and plot with pandas
# get the price chart for a given product in each store that it is recorded


import pandas as pd
import matplotlib.pyplot as plt
from sqlalchemy import func, desc


# get product data given sku
def get_product_data(session: sessionmaker, sku: str) -> Product:
    """
    Get product data for a given SKU.
    Args:
        session (Session): The SQLAlchemy session.
        sku (str): The SKU of the product.
    Returns:
        Product: The product data.
    """
    return session.query(Product).filter_by(sku=sku).first()

def get_latest_store_product(session: sessionmaker, product: Product) -> StoreProduct:
    """
    Get the latest store product entry for a given product.
    Args:
        session (Session): The SQLAlchemy session.
        product (Product): The product to get store product for.
    Returns:
        StoreProduct: The latest store product entry.
    """
    return session.query(StoreProduct).filter_by(product=product).order_by(StoreProduct.dt_price_scraped_at.desc()).first()

def get_price_history(session: sessionmaker, product: Product) -> pd.DataFrame:
        """
        Get price history for a given product across all stores.
        Args:
            session (Session): The SQLAlchemy session.
            product (Product): The product to get price history for. 
        Returns:
            DataFrame: Price history data with store, date and price columns.
        """
        query = session.query(
            Store.name.label('store'),
            PriceHistory.dt_price_scraped_at,
            PriceHistory.price
        ).select_from(PriceHistory).join(
            StoreProduct, PriceHistory.store_product_id == StoreProduct.id
        ).join(
            Store, StoreProduct.store_id == Store.id
        ).filter(
            StoreProduct.product_id == product.id
        ).order_by(
            Store.name,
            PriceHistory.dt_price_scraped_at
        )
        
        df = pd.read_sql(query.statement, session.bind)
        df['dt_price_scraped_at'] = pd.to_datetime(df['dt_price_scraped_at'])
        # add product name metadata
        df['product'] = product.name
        return df

def plot_price_history(df: pd.DataFrame):
    """
    Plot price history from a dataframe with a line for each store.
    Args:
        df (DataFrame): Price history dataframe with store, date and price columns
    """
    plt.figure(figsize=(15,8))
    
    # Plot each store as a separate line
    for store in sorted(df['store'].unique()):
        store_data = df[df['store'] == store]
        plt.plot(store_data['dt_price_scraped_at'], 
                store_data['price'], 
                'o-',  # Line with markers
                label=store,
                markersize=4)
    
    plt.title(f"Price History for {df["product"].iloc[0]}")
    plt.xlabel('Date', labelpad=10)
    plt.ylabel('Price (ZAR)', labelpad=10)
    
    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45)
    
    # Add legend outside of plot
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # Adjust layout to prevent label cutoff
    plt.tight_layout()
    
    # Add grid for better readability
    plt.grid(True, linestyle='--', alpha=0.7)


# find product with the most entries in PriceHistory
def get_product_with_most_price_history_entries(session: sessionmaker) -> Product:
    """
    Get the product with the most price entries in the PriceHistory table.
    Args:
        session (Session): The SQLAlchemy session.
    Returns:
        Product: The product with the most price entries.
    """
    query = session.query(
        Product,
        PriceHistory
    ).select_from(PriceHistory).join(
        StoreProduct, PriceHistory.store_product_id == StoreProduct.id
    ).join(
        Product, StoreProduct.product_id == Product.id
    ).group_by(
        Product.id
    ).order_by(
        PriceHistory.id.desc()
    ).limit(1)
    
    return query.first()[0]

def get_top_products_with_most_store_product_entries(session: sessionmaker, limit: int = 5) -> List[Product]:
    """
    Get the top products with the most store product entries in the StoreProduct table.
    Args:
        session (Session): The SQLAlchemy session.
        limit (int): Number of products to return. Defaults to 5.
    Returns:
        List[Product]: List of products with the most store product entries.
    """
    query = session.query(
        Product,
        func.count(StoreProduct.id).label('store_count')
    ).select_from(StoreProduct).join(
        Product, StoreProduct.product_id == Product.id
    ).group_by(
        Product.id
    ).order_by(
        desc('store_count')
    ).limit(limit)
    
    return [result[0] for result in query.all()]

# Get top 5 products
top_products = get_top_products_with_most_store_product_entries(session=Session())
for product in top_products:
    print(f"Product: {product}")


# Example 1: Get product data for a specific SKU
sku = "000000000000192237_CK"
print(f"Getting product data for SKU: {sku}")
product = get_product_data(session=session, sku=sku)
print(f"Product found: {product.name}, SKU: {product.sku}")



# Example 2: Get the latest store product entry
latest_store_product = get_latest_store_product(session=session, product=product)

print(f"""
Latest price record:
Store: {latest_store_product.store.name}
Price: R{latest_store_product.price}
Date: {latest_store_product.dt_price_scraped_at}
""")


# Example 3: Get price history and create plot
price_history_df = get_price_history(session, product)
print("\nPrice History DataFrame:")
print(price_history_df.head())



# Create the plot
plot_price_history(price_history_df)
plt.show()

price_history_df.iloc[1:3,:]


# map

# get uncleaned entries for retailer == pnp.co.za

with Session() as session:
    retailer = session.query(Retailer).filter_by(url=MAIN_RETAILER_URL).first()
    
    
    # <ScrapedData>
    uncleaned_scraped_data = read_uncleaned_scraped_data(session=session, retailer=retailer)
    raw_products_on_page_json:dict = json.loads(uncleaned_scraped_data.data)

    # <Store>
    current_store:Store = uncleaned_scraped_data.store
    # print(f"len(raw_products_on_page_json): '{len(raw_products_on_page_json)}")
    for raw_product_json in raw_products_on_page_json:
        
        # <Product>
        mapped_product_data = map_scraped_data_to_product(raw_product_json=raw_product_json, uncleaned_scraped_data=uncleaned_scraped_data,PNP_LIQUOR_IDENTIFYING_CATEGORY=PNP_LIQUOR_IDENTIFYING_CATEGORY)
        # print(f"mapped_product_data: {mapped_product_data}")
        was_created, cleaned_product = create_or_update_product(
            session=session,
            sku = mapped_product_data["sku"],
            name = mapped_product_data["name"],
            url = mapped_product_data["url"],
            is_liquor = mapped_product_data["is_liquor"],
            date_scraped = mapped_product_data["date_scraped"],
            thumbnail_image_url = mapped_product_data["thumbnail_image_url"],
            detail_image_url = mapped_product_data["detail_image_url"],
            description = mapped_product_data["description"],
            retailer = mapped_product_data["retailer_id"],
        )
        # print(f"create_or_update_product: {was_created, cleaned_product}")

        # <StoreProduct>
        mapped_store_product_data = map_scraped_data_to_store_product(raw_product_json=raw_product_json, uncleaned_scraped_data=uncleaned_scraped_data,current_store=current_store)
        # print(f"mapped_store_product_data: {mapped_store_product_data}")
        was_created, cleaned_store_product = create_or_update_store_product(
            session=session,
            product=cleaned_product,
            store=current_store,
            scrape_type=mapped_store_product_data['scrape_type'],
            price=mapped_store_product_data['price'],
            is_priced_per_unit=mapped_store_product_data['is_priced_per_unit'],
            price_per_unit=mapped_store_product_data['price_per_unit'],
            dt_price_scraped_at=mapped_store_product_data['dt_price_scraped_at'],
            is_in_stock=mapped_store_product_data['is_in_stock'],
            dt_is_in_stock_scraped_at=mapped_store_product_data['dt_is_in_stock_scraped_at'],
            stock_count=mapped_store_product_data['stock_count'],
            dt_stock_count_scraped_at=mapped_store_product_data['dt_stock_count_scraped_at'],
            is_on_promotion=mapped_store_product_data['is_on_promotion'],
        )
        print(f"create_or_update_store_product: {was_created, cleaned_store_product}")

        # <PriceHistory>
        # use the `mapped_store_product_data` data to create a price history
        price_history = create_or_update_price_history(
            session=session,
            store_product=cleaned_store_product,
            price=mapped_store_product_data['price'],
            price_per_unit=mapped_store_product_data['price_per_unit'],
            dt_price_scraped_at=mapped_store_product_data['dt_price_scraped_at']
        )
        print(f"create_or_update_price_history: {price_history}")

        # <StockHistory>
        # use the `mapped_store_product_data` data to create a stock history
        stock_history = create_or_update_stock_history(
            session=session,
            store_product=cleaned_store_product,
            is_in_stock=mapped_store_product_data['is_in_stock'],
            dt_is_in_stock_scraped_at=mapped_store_product_data['dt_is_in_stock_scraped_at'],
            stock_count=mapped_store_product_data['stock_count'],
            dt_stock_count_scraped_at=mapped_store_product_data['dt_stock_count_scraped_at']
        )
        print(f"create_or_update_stock_history: {stock_history}")



        
        print(f"{'-'*50}")
# raw_products_on_page_json


with Session() as session:  
    retailer = session.query(Retailer).filter_by(url=MAIN_RETAILER_URL).first()

# _______________________
# Product Table groupings:

# info:
# 'id', 'sku', 'barcode', 'name', 'slug', 'url', 'unit_of_measurement', 'quantity_of_unit', 'is_liquor', 'description',

# metadata:
# 'date_scraped', 'date_created', 'date_updated', 

# relationships:
# 'store_products', 'retailer_id'
# _______________________



# # scraped data has more keys and must be wittled down to the keys that are in the Product table
# # SrapedData.data[i]: dict_keys(['available', 'brandSellerId', 'categoryNames', 'code', 'defaultQuantityOfUom', 'images', 'inStockIndicator', 'name', 'numberOfReviews', 'price', 'sponsoredProduct', 'stock', 'tvLicenceRequired'])

# # Product: ['id', 'sku', 'barcode', 'name', 'slug', 'url', 'unit_of_measurement', 'quantity_of_unit', 'is_liquor', 'date_scraped', 'date_created', 'date_updated', 'thumbnail_image_url', 'detail_image_url', 'description', 'retailer_id']

# # sku: check if `code` is in the raw_product_json
# if 'code' in raw_product_json:
#     sku = raw_product_json['code']
# print(f'sku: {sku}')

# # barcode: check if `brandSellerId` is in the raw_product_json
# # none yet

# # name: check if `name` is in the raw_product_json
# if 'name' in raw_product_json:
#     name = raw_product_json['name']
# print(f'name: {name}')

# # url
# url = 'https://www.pnp.co.za' + '/p/' + sku
# print(f'url: {url}')

# # unit_of_measurement:
# # quantity_of_unit

# # is_liquor: check if `categoryNames` exists and contains 'Liquor Store'
# if 'categoryNames' in raw_product_json:
#     is_liquor = PNP_LIQUOR_IDENTIFYING_CATEGORY in raw_product_json['categoryNames']
# print(f'is_liquor: {is_liquor}')


# # dt_scraped: found in `uncleaned_product_data`
# cleaned_product_data_date_scraped = uncleaned_product_data.dt_scraped
# if not cleaned_product_data_date_scraped:
#     raise ValueError('date_scraped not found in uncleaned_product_data')
# print(f'cleaned_product_data_date_scraped: {cleaned_product_data_date_scraped}')

# # Images: check if `images` is in the raw_product_json for 'thumbnail_image_url', 'detail_image_url' equivalent images
# if 'images' in raw_product_json:
#     # thumbnail_image_url is where format is 'thumbnail' in 'imgaes' list
#     cleaned_data_thumbnail_image_url = None
#     cleaned_data_detail_image_url = None
#     for raw_product_json_image in raw_product_json['images']:
#         if raw_product_json_image['format'] == 'thumbnail':
#             cleaned_data_thumbnail_image_url = raw_product_json_image['url']
#         elif raw_product_json_image['format'] == 'product':
#             cleaned_data_detail_image_url = raw_product_json_image['url']
# else:
#     raise ValueError('images not found in raw_product_json')

# print(f'thumbnail_image_url: {cleaned_data_thumbnail_image_url}')
# print(f'detail_image_url: {cleaned_data_detail_image_url}')

# # description: none yet
# cleaned_data_description = None

# # retailer_id: found in `uncleaned_product_data`
# cleaned_data_retailer = uncleaned_product_data.retailer
# print(f'retailer_id: {cleaned_data_retailer}')


map_scraped_data_to_product(raw_product_json=raw_product_json, uncleaned_product_data=uncleaned_product_data,PNP_LIQUOR_IDENTIFYING_CATEGORY=PNP_LIQUOR_IDENTIFYING_CATEGORY)

# was_created, cleaned_product = create_or_update_product(
#         session=session,
#         sku = product_data["sku"],
#         name = product_data["name"],
#         url = product_data["url"],
#         is_liquor = product_data["is_liquor"],
#         date_scraped = product_data["date_scraped"],
#         thumbnail_image_url = product_data["thumbnail_image_url"],
#         detail_image_url = product_data["detail_image_url"],
#         description = product_data["description"],
#         retailer = product_data["retailer_id"],
#     )
# was_created, cleaned_product


# was_created, cleaned_product = create_or_update_product(
#     session=session,
#     sku=sku,
#     name=name,
#     url=url,
#     is_liquor=is_liquor,
#     date_scraped=cleaned_product_data_date_scraped,
#     thumbnail_image_url=cleaned_data_thumbnail_image_url,
#     detail_image_url=cleaned_data_detail_image_url,
#     description=cleaned_data_description,
#     retailer=cleaned_data_retailer
# )

# #  _______________________
# # StoreProduct Table groupings:

# # prices:
# # 'price', 'is_priced_per_unit', 'price_per_unit', 

# # stock count:
# # 'is_in_stock', 'stock_count',

# # promos:
# # 'is_on_promotion',
# # metadata:
# # 'dt_price_scraped_at', 'dt_is_in_stock_scraped_at', 'dt_stock_count_scraped_at', 'dt_created', 'dt_updated'

# # relationships:
# # 'store_id', 'product_id'
# # _______________________

# # StoreProduct: ['id', 'store_id', 'product_id', 'price', 'is_priced_per_unit', 'price_per_unit', 'dt_price_scraped_at', 'is_in_stock', dt_is_in_stock_scraped_at, 'stock_count', 'dt_stock_count_scraped_at', 'is_on_promotion', 'dt_created', 'dt_updated']

# # prices:
# # price: check if `price.value` is in the raw_product_json
# if 'price' in raw_product_json:
#     # check if `price.value` is in the raw_product_json
#     raw_product_price = raw_product_json['price']['value']
# print(f'price: {raw_product_price}')

# # is_priced_per_unit: none yet
# print(f'is_priced_per_unit: {None}')
# # price_per_unit: none yet
# print(f'price_per_unit: {None}')

# # dt_price_scraped_at: found in `uncleaned_product_data`
# cleaned_product_data_dt_price_scraped_at = uncleaned_product_data.dt_scraped
# print(f'dt_price_scraped_at: {cleaned_product_data_dt_price_scraped_at}')

# # stock count:
# # is_in_stock: check if `stock.stockLevelStatus` is in the raw_product
# if 'stock' in raw_product_json:
#     is_in_stock = raw_product_json['stock']['stockLevelStatus'] == 'inStock'
# print(f'is_in_stock: {is_in_stock}')

# # dt_is_in_stock_scraped_at: found in `uncleaned_product_data` and is the same as `dt_price_scraped_at` since not stock count api call has yet been made
# cleaned_product_data_dt_is_in_stock_scraped_at = cleaned_product_data_dt_price_scraped_at
# print(f'dt_is_in_stock_scraped_at: {cleaned_product_data_dt_is_in_stock_scraped_at}')


# # stock_count: none yet 

# # dt_stock_count_scraped_at: found in `uncleaned_product_data` and is the same as `dt_price_scraped_at` since not stock count api call has yet been made
# cleaned_product_data_dt_stock_count_scraped_at = cleaned_product_data_dt_price_scraped_at
# print(f'dt_stock_count_scraped_at: {cleaned_product_data_dt_stock_count_scraped_at}')

# # is_on_promotion: look for `potentialPromotions` in raw_product_json
# if 'potentialPromotions' in raw_product_json:
#     # check length of `potentialPromotions` list
#     is_on_promotion = len(raw_product_json['potentialPromotions']) > 0
# print(f'is_on_promotion: {is_on_promotion}')


# # relationships:
# # 'store_id', 'product_id'


# mapped_store_product_data['store_id'],
# mapped_store_product_data['product_id'],

mapped_store_product_data['price'],
mapped_store_product_data['is_priced_per_unit'],
mapped_store_product_data['price_per_unit'],
mapped_store_product_data['dt_price_scraped_at'],
mapped_store_product_data['is_in_stock'],
mapped_store_product_data['dt_is_in_stock_scraped_at'],
mapped_store_product_data['stock_count'],
mapped_store_product_data['dt_stock_count_scraped_at'],
mapped_store_product_data['is_on_promotion'],

was_created, cleaned_store_product = create_or_update_store_product(
    session=session,
    product=cleaned_product,
    store=current_store,
    price=mapped_store_product_data['price'],
    is_priced_per_unit=mapped_store_product_data['is_priced_per_unit'],
    price_per_unit=mapped_store_product_data['price_per_unit'],
    dt_price_scraped_at=mapped_store_product_data['dt_price_scraped_at'],
    is_in_stock=mapped_store_product_data['is_in_stock'],
    dt_is_in_stock_scraped_at=mapped_store_product_data['dt_is_in_stock_scraped_at'],
    stock_count=mapped_store_product_data['stock_count'],
    dt_stock_count_scraped_at=mapped_store_product_data['dt_stock_count_scraped_at'],
    is_on_promotion=mapped_store_product_data['is_on_promotion'],
)

was_created, cleaned_store_product

mapped_store_product_data = map_scraped_data_to_store_product(raw_product_json, uncleaned_scraped_data)
print(f"mapped_store_product_data: {mapped_store_product_data}")

was_created, cleaned_store_product = create_or_update_store_product(
    session=session,
    product=cleaned_product,
    store=uncleaned_product_store,
    price=raw_product_price,
    is_priced_per_unit=None,
    price_per_unit=None,
    dt_price_scraped_at=cleaned_product_data_dt_price_scraped_at,
    is_in_stock=is_in_stock,
    stock_count=None,
    dt_stock_count_scraped_at=cleaned_product_data_dt_stock_count_scraped_at,
    is_on_promotion=is_on_promotion,
    scrape_type=uncleaned_product_data.scrape_type
)

was_created, cleaned_store_product

# CAREFUL WHICH FILEDS ARE UPDATAED. THIS DEPENDS ON THE TYPE OF SCRAPE since they all offer different information about a product.
# if uncleaned_product_data.scrape_type == ScrapeType.CATEGORY.value:
    # pass in the `price`, `dt_price_scraped_at`, `is_in_stock`, `dt_is_in_stock_scraped_at`, and `is_on_promotion` variables for update
# if uncleaned_product_data.scrape_type == ScrapeType.DETAILS.value:
    # pass in all variables

def create_or_update_store_product(
    session: sessionmaker, 
    product: Product, 
    store: Store, 
    price: float, 
    is_priced_per_unit: Optional[bool], 
    price_per_unit: Optional[float], 
    dt_price_scraped_at: datetime, 
    is_in_stock: bool, 
    stock_count: Optional[int], 
    dt_stock_count_scraped_at: datetime, 
    is_on_promotion: bool, 
    scrape_type: ScrapeType
    ) -> Tuple[bool, StoreProduct]:
    """
    Create or update a store product.
    Args:
        session (Session): The SQLAlchemy session.
        product (Product): The product.
        store (Store): The store.
        price (float): The price of the product.
        is_priced_per_unit (bool): Whether the product is priced per unit.
        price_per_unit (float): The price per unit of the product.
        dt_price_scraped_at (datetime): The date the price was scraped.
        is_in_stock (bool): Whether the product is in stock.
        stock_count (int): The stock count of the product.
        dt_stock_count_scraped_at (datetime): The date the stock count was scraped.
        is_on_promotion (bool): Whether the product is on promotion.
        scrape_type (ScrapeType): The type of scrape.
    Returns:
        StoreProduct: The updated or created store product.
    """
    store_product = session.query(StoreProduct).filter_by(product=product, store=store).first()
    created = False
    if store_product is None:
        print(f'Creating store product with product: {product.id} and store: {store.id}')
        created = True
        store_product = StoreProduct(
            product=product,
            store=store,
            price=price,
            is_priced_per_unit=is_priced_per_unit,
            price_per_unit=price_per_unit,
            dt_price_scraped_at=dt_price_scraped_at,
            is_in_stock=is_in_stock,
            stock_count=stock_count,
            dt_stock_count_scraped_at=dt_stock_count_scraped_at,
            is_on_promotion=is_on_promotion
        )
        session.add(store_product)
    else:
        print(f'Updating store product with product: {product.id} and store: {store.id}')
        if scrape_type == ScrapeType.CATEGORY.value:
            store_product.price = price
            store_product.dt_price_scraped_at = dt_price_scraped_at
            store_product.is_in_stock = is_in_stock
            store_product.dt_stock_count_scraped_at = dt_stock_count_scraped_at
            store_product.is_on_promotion = is_on_promotion
            store_product.dt_updated = datetime.now()
        elif scrape_type == ScrapeType.DETAILS.value:
            store_product.price = price
            store_product.is_priced_per_unit = is_priced_per_unit
            store_product.price_per_unit = price_per_unit
            store_product.dt_price_scraped_at = dt_price_scraped_at
            store_product.is_in_stock = is_in_stock
            store_product.stock_count = stock_count
            store_product.dt_stock_count_scraped_at = dt_stock_count_scraped_at
            store_product.is_on_promotion = is_on_promotion
            store_product.dt_updated = datetime.now()
    session.commit()
    return created, store_product

was_created, cleaned_store_product = create_or_update_store_product(
    session=session,
    product=cleaned_product,
    store=uncleaned_product_store,
    price=raw_product_price,
    is_priced_per_unit=None,
    price_per_unit=None,
    dt_price_scraped_at=cleaned_product_data_dt_price_scraped_at,
    is_in_stock=is_in_stock,
    stock_count=None,
    dt_stock_count_scraped_at=cleaned_product_data_dt_stock_count_scraped_at,
    is_on_promotion=is_on_promotion,
    scrape_type=uncleaned_product_data.scrape_type
)

was_created, cleaned_store_product



# # create or get store product
# with Session() as session:
#     cleaned_store_product = session.query(StoreProduct).filter_by(
#         store=uncleaned_product_store,
#         product=cleaned_product,
#         dt_price_scraped_at=cleaned_product_data_dt_price_scraped_at,
#     ).first()

#     if cleaned_store_product is None:
#         print(f'creating store product with store: {uncleaned_product_store} and product: {cleaned_product}')
#         cleaned_store_product = StoreProduct(
#             store=uncleaned_product_store,
#             product=cleaned_product,
#             price=raw_product_price,
#             dt_price_scraped_at=cleaned_product_data_dt_price_scraped_at,
#             is_in_stock=is_in_stock,
#             dt_is_in_stock_scraped_at=cleaned_product_data_dt_is_in_stock_scraped_at,
#             # stock_count=None,
#             # dt_stock_count_scraped_at=cleaned_product_data_dt_stock_count_scraped_at,
#             is_on_promotion=is_on_promotion
#         )
#         session.add(cleaned_store_product)
#         session.commit()
#     else:
#         print(f'store product with store: {uncleaned_product_store} and product: {cleaned_product} already exists')
#         # check if there are any fields that are different

#         # UPDATE STORE PRODUCT

#     print(f"cleaned_store_product: {cleaned_store_product}")





# _______________________
# PriceHistory Table groupings:

# PRICES:
# 'price', 'price_per_unit',
# RELATIONSHIPS:
# 'store_product_id'
# METADATA:
# 'date_scraped'
# _______________________

# PriceHistory: ['id', 'store_product_id', 'price', 'price_per_unit', 'date_scraped']

# create price history
# cleaned_price_history = PriceHistory(
#     store_product=cleaned_store_product,
#     price=raw_product_price,
#     # price_per_unit=None,
#     date_scraped=cleaned_product_data_dt_price_scraped_at
# )
# cleaned_price_history

# create or get PriceHistory
with Session() as session:
    cleaned_price_history = session.query(PriceHistory).filter_by(
        store_product=cleaned_store_product,
        price=raw_product_price,
        # price_per_unit=None,
        date_scraped=cleaned_product_data_dt_price_scraped_at
        ).first()
    
    if not cleaned_price_history:
        print("creating 'cleaned_price_history'")
        # create price history
        cleaned_price_history = PriceHistory(
            store_product=cleaned_store_product,
            price=raw_product_price,
            # price_per_unit=None,
            date_scraped=cleaned_product_data_dt_price_scraped_at
        )

        session.add(cleaned_price_history)
        session.commit()
    else:
        print("already exist. Write code to update?")
    

# _______________________
# StockHistory Table groupings:

# stock count:
# 'stock_status', 'stock_count',
# relationships:
# 'store_product_id'
# metadata:
# 'date_scraped'
# _______________________


# StockHistory: ['id', 'store_product_id', 'stock_status', 'stock_count', 'date_scraped']

# create stock history
# cleaned_stock_history = StockHistory(
#     store_product=cleaned_store_product,
#     stock_status=is_in_stock,
#     # stock_count=None,
#     date_scraped=cleaned_product_data_dt_stock_count_scraped_at
# )

with Session() as session:
    cleaned_stock_history = session.query(StockHistory).filter_by(
        store_product=cleaned_store_product,
        stock_status=is_in_stock,
        # stock_count=None,
        date_scraped=cleaned_product_data_dt_stock_count_scraped_at
    ).first()
    
    if not cleaned_stock_history:
        print('creating "cleaned_stock_history" entry')
        cleaned_stock_history = StockHistory(
            store_product=cleaned_store_product,
            stock_status=is_in_stock,
            # stock_count=None,
            date_scraped=cleaned_product_data_dt_stock_count_scraped_at
        )
        
        session.add(cleaned_stock_history)
        session.commit()

    else:
        print("updating cleaned_stock_history?")



{'available': True,
 'brandSellerId': 'KIMBERLY CLARK OF SA',
 'categoryNames': ['All Products',
  'Baby',
  'Baby Nappies & Changing',
  'Baby Nappies',
  'Large - Size 4-5 From 7-18kg'],
 'code': '000000000000772237_EA',
 'defaultQuantityOfUom': 1,
 'images': [{'format': 'thumbnail',
   'imageType': 'PRIMARY',
   'url': 'https://cdn-prd-02.pnp.co.za/sys-master/images/h17/h3b/11366322667550/silo-product-image-v2-19Feb2024-100132-6001019910711-Straight_on-205063-2593_96Wx96H'},
  {'format': 'product',
   'imageType': 'PRIMARY',
   'url': 'https://cdn-prd-02.pnp.co.za/sys-master/images/hcb/h83/11366324043806/silo-product-image-v2-19Feb2024-100132-6001019910711-Straight_on-205063-2593_400Wx400H'},
  {'format': 'carousel',
   'imageType': 'PRIMARY',
   'url': 'https://cdn-prd-02.pnp.co.za/sys-master/images/h88/h9a/11366321979422/silo-product-image-v2-19Feb2024-100132-6001019910711-Straight_on-205063-2593_300Wx300H'},
  {'format': 'listing',
   'imageType': 'PRIMARY',
   'url': 'https://cdn-prd-02.pnp.co.za/sys-master/images/h6a/h94/11366324502558/silo-product-image-v2-19Feb2024-100132-6001019910711-Straight_on-205063-2593_140Wx140H'}],
 'inStockIndicator': True,
 'name': 'Huggies Gold Size 4 (8-14kg) Value Pack 50 Nappies',
 'numberOfReviews': 0,
 'potentialPromotions': [{'code': 'SCRIPT20240613143921-3000344205',
   'endDate': '2024-06-23T21:59:59+0000',
   'promotionDisplayType': 'SAVE',
   'promotionTextMessage': 'Click For Incredible Freebie',
   'startDate': '2024-06-17T22:00:00+0000',
   'valid': False}],
 'price': {'average': False,
  'currencyIso': 'ZAR',
  'formattedValue': 'R264.99',
  'oldPrice': 0,
  'oldPriceFormattedValue': 'R0.00',
  'onlineOnlyPrice': False,
  'priceType': 'BUY',
  'savings': 0,
  'savingsFormattedValue': 'R0.00',
  'value': 264.99},
 'productDisplayBadges': [{'displayGroup': 'group4', 'displayName': 'SAVE'}],
 'sponsoredProduct': False,
 'stock': {'stockLevelStatus': 'inStock'},
 'tvLicenceRequired': False}



# get the current retailer, pnp
# retailer = Session().query(Retailer).filter_by(name='pnp').first()

# read ScrapedData and find all the rows where 

WW_LIQUOR_IDENTIFYING_CATEGORY = "Liquor Store"
MAIN_RETAILER_URL = 'https://www.woolworths.co.za'

def read_uncleaned_scraped_data(
        session: sessionmaker, 
        retailer: Retailer, 
        scrape_type: str = ScrapeType.CATEGORY.value            
        ) -> ScrapedData:
    """
    Create or update a product.
    Args:
        session (Session): The SQLAlchemy session.
        retailer (Retailer): The retailer of the product.
    Returns:
        ScrapedData: returns a ScrapedData object.
        e.g "<ScrapedData(retailer=Pick n Pay, store=PnP Hyper Somerset Mall, scrape_type=category, dt_scraped=2024-06-21 15:43:40)>"

    """
    uncleaned_scraped_data = session.query(ScrapedData).filter(
        ScrapedData.retailer_id == retailer.id,
        ScrapedData.is_cleaned == False,
        ScrapedData.scrape_type == scrape_type
    ).options(joinedload(ScrapedData.store)).first()
    
    return uncleaned_scraped_data


with Session() as session:
    retailer = session.query(Retailer).filter_by(url=MAIN_RETAILER_URL).first()
    print(retailer)


    # <ScrapedData>
    uncleaned_scraped_data = read_uncleaned_scraped_data(session=session, retailer=retailer)
    print(uncleaned_scraped_data)
    # raw_products_on_page_json:dict = json.loads(uncleaned_scraped_data.data)


CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES:list = [
    "/Spirit-Coolers",
    "/Spirits-and-Liqueurs",
    "/Wine",
    "/Beer-and-Cider",
]
MAIN_RETAILER_URL = 'https://www.checkers.co.za'

def read_uncleaned_scraped_data(
        session: sessionmaker, 
        retailer: Retailer, 
        scrape_type: str = ScrapeType.CATEGORY.value            
        ) -> ScrapedData:
    """
    Create or update a product.
    Args:
        session (Session): The SQLAlchemy session.
        retailer (Retailer): The retailer of the product.
    Returns:
        ScrapedData: returns a ScrapedData object.
        e.g "<ScrapedData(retailer=Pick n Pay, store=PnP Hyper Somerset Mall, scrape_type=category, dt_scraped=2024-06-21 15:43:40)>"

    """
    uncleaned_scraped_data = session.query(ScrapedData).filter(
        ScrapedData.retailer_id == retailer.id,
        ScrapedData.is_cleaned == False,
        ScrapedData.scrape_type == scrape_type
    ).options(joinedload(ScrapedData.store)).first()
    
    return uncleaned_scraped_data

# mapper helpers
def checkers_get_is_liquor_attribute_for_mapper(raw_product_json:Dict)-> str:
    return raw_product_json['product_url'].split('/p/')[-1]

def checkers_get_is_liquor_attribute(raw_product_json:Dict, CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES:list)->bool:
    liquor_count:int = sum([
            True for CHECKERS_LIQUOR_IDENTIFYING_CATEGORY in CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES 
            if CHECKERS_LIQUOR_IDENTIFYING_CATEGORY in raw_product_json["product_url"]
        ])
    if liquor_count>0:
        return True
    else:
        return False
    
def checkers_get_price_for_mapper(raw_product_json:Dict)-> float:
    # check if `information.0.htmlBBs.price_now` is in the raw_product_json else raise
    if 'information.0.price.value' in raw_product_json:
        return raw_product_json['information.0.price.value']
    else:
        raise ValueError('information.0.price.value not found in raw_product_json')
    
    
def checkers_map_scraped_data_to_product(
        raw_product_json: Dict,
        uncleaned_scraped_data: ScrapedData,
        CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES: str,

        # Fucntions
        checkers_get_is_liquor_attribute_for_mapper: callable,
        checkers_get_is_liquor_attribute: callable


        ) -> Dict:
    """
    Maps raw scraped JSON data to the columns of a Product table in a structured format.
    
    Parameters:
        raw_product_json (Dict): The raw JSON data containing product information. A single entry in ScrapedData.data.
        uncleaned_scraped_data (ScrapedData ): A ScrapedData object.
        CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES (str): String identifier used to determine 
                                               if a product belongs to the liquor category.


        FUNCTIONS:
        checkers_get_is_liquor_attribute_for_mapper.
        checkers_get_is_liquor_attribute.
    
    Returns:
        Dict: A dictionary containing the cleaned product data, structured for the Product table.
              Keys correspond to column names in the Product table, values are derived from 
              the raw JSON and metadata.
    
    Columns Outputted:
        - id: None (not provided by scraped data; managed by the database)
        - sku: Product identifier from raw JSON `code`.
        - barcode: None (not currently mapped but could be derived in future).
        - name: Product name from raw JSON `name`.
        - slug: None (could be generated if required).
        - url: Constructed URL based on the product `code`.
        - unit_of_measurement: None (not currently mapped).
        - quantity_of_unit: None (not currently mapped).
        - is_liquor: Boolean flag derived from `categoryNames` in raw JSON.
        - date_scraped: Scrape timestamp from `uncleaned_scraped_data.dt_scraped`.
        - date_created: None (could be auto-generated by the database).
        - date_updated: None (could be auto-generated by the database).
        - thumbnail_image_url: URL from `images` where format is `thumbnail`.
        - detail_image_url: URL from `images` where format is `product`.
        - description: None (placeholder for potential mapping).
        - retailer_id: Retailer identifier from `uncleaned_scraped_data.retailer`.
    """
    # Initialize product dictionary with default None values
    product_data = {
        "id": None,
        "sku": None,
        "barcode": None,
        "name": None,
        "slug": None,
        "url": None,
        "unit_of_measurement": None,
        "quantity_of_unit": None,
        "is_liquor": None,
        "date_scraped": None,
        "date_created": None,
        "date_updated": None,
        "thumbnail_image_url": None,
        "detail_image_url": None,
        "description": None,
        "retailer_id": None,
    }
    
    # Map `sku` from `code`
    # if "id" in raw_product_json:
    product_data["sku"] = checkers_get_is_liquor_attribute_for_mapper(raw_product_json=raw_product_json)
    
    # Map `name`
    if "name" in raw_product_json:
        product_data["name"] = raw_product_json["name"]
    
    # Construct `url`
    if product_data["sku"]:
        product_data["url"] = f'https://www.checkers.co.za/p/{product_data["sku"]}'
    else:
        raise ValueError("sku not found in uncleaned_scraped_data")

    
    # Map `is_liquor` based on `product_url`
    if "product_url" in raw_product_json:
        product_data["is_liquor"] = checkers_get_is_liquor_attribute(raw_product_json=raw_product_json, CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES=CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES)
    else:
        raise ValueError("product_url not found in uncleaned_scraped_data")
    
    # Map `date_scraped` from uncleaned product data
    product_data["date_scraped"] = getattr(uncleaned_scraped_data, "dt_scraped", None)
    if not product_data["date_scraped"]:
        raise ValueError("date_scraped not found in uncleaned_scraped_data")
    
    # Map image URLs
    if "product_image_url" in raw_product_json:
        product_data["thumbnail_image_url"] = raw_product_json.get("product_image_url")
        product_data["detail_image_url"] = raw_product_json.get("product_image_url")
    
    # Map `retailer_id`
    product_data["retailer_id"] = getattr(uncleaned_scraped_data, "retailer", None)
    if not product_data["retailer_id"]:
        raise ValueError("retailer_id not found in uncleaned_scraped_data")
    
    return product_data


def create_or_update_product(
        session: sessionmaker, 
        sku: str, 
        name: str, 
        url: str, 
        is_liquor: bool, 
        date_scraped: datetime, 
        thumbnail_image_url: str, 
        detail_image_url: str, 
        description: str, 
        retailer: Retailer
        
        # functions to update the product
        
        ) -> Tuple[bool, Product]:
    """
    Create or update a product.
    Args:
        session (Session): The SQLAlchemy session.
        sku (str): The SKU of the product.
        name (str): The name of the product.
        url (str): The URL of the product.
        is_liquor (bool): Whether the product is a liquor.
        date_scraped (datetime): The date the product was scraped.
        thumbnail_image_url (str): The URL of the thumbnail image.
        detail_image_url (str): The URL of the detail image.
        description (str): The description of the product.
        retailer (Retailer): The retailer of the product.
    Returns:
        Product: The updated or created product.
        e.g "(True, <Product(name=Huggies Gold Size 4 (8-14kg) Value Pack 50 Nappies, sku=000000000000772237_EA)>)"
    """
    product = session.query(Product).filter_by(sku=sku, retailer=retailer).first()
    created = False
    if product is None:
        print(f'Creating product with sku: {sku}')
        created = True
        product = Product(
            sku=sku,
            name=name,
            url=url,
            is_liquor=is_liquor,
            date_scraped=date_scraped,
            thumbnail_image_url=thumbnail_image_url,
            detail_image_url=detail_image_url,
            description=description,
            retailer=retailer
        )
        session.add(product)
    else:
        print(f'Updating product with sku: {sku}???')
        product.name = name
        product.url = url
        product.is_liquor = is_liquor
        product.date_scraped = date_scraped
        product.thumbnail_image_url = thumbnail_image_url
        product.detail_image_url = detail_image_url
        product.description = description
        product.retailer = retailer
    session.commit()
    return created, product


def checkers_map_scraped_data_to_store_product(
        raw_product_json: Dict,
        uncleaned_scraped_data: ScrapedData, 
        current_store:Store,

        # FUNCTIONS

        ) -> Dict:
    """
    Maps raw scraped JSON data to the columns of a StoreProduct table in a structured format.

    Parameters:
        raw_product_json (Dict): The raw JSON data containing product and store information.
        uncleaned_scraped_data (object): An object containing additional metadata 
                                          (e.g., `dt_scraped` for scraping timestamps).
        current_store (Store): Store table object.

    Returns:
        Dict: A dictionary containing the cleaned store-product data, structured for the StoreProduct table.
              Keys correspond to column names in the StoreProduct table, values are derived from 
              the raw JSON and metadata.

    Columns Outputted:
        - id: None (managed by the database)
        - store_id: None (placeholder for store ID from external data)
        - product_id: None (placeholder for product ID from external data)
        - price: Product price from `price.value` in raw JSON.
        - is_priced_per_unit: None (not currently mapped).
        - price_per_unit: None (not currently mapped).
        - dt_price_scraped_at: Timestamp when price was scraped from `uncleaned_scraped_data.dt_scraped`.
        - is_in_stock: Boolean flag based on `stock.stockLevelStatus` in raw JSON.
        - dt_is_in_stock_scraped_at: Timestamp from `uncleaned_scraped_data.dt_scraped`.
        - stock_count: None (not currently mapped).
        - dt_stock_count_scraped_at: Timestamp from `uncleaned_scraped_data.dt_scraped`.
        - is_on_promotion: Boolean flag based on the presence of `potentialPromotions` in raw JSON.
        - dt_created: None (placeholder for database-generated timestamp).
        - dt_updated: None (placeholder for database-generated timestamp).
    """
    # Initialize store-product dictionary with default None values
    store_product_data = {
        "id": None,
        "store_id": None,
        "product_id": None,
        "scrape_type": None,
        "price": None,
        "is_priced_per_unit": None,
        "price_per_unit": None,
        "dt_price_scraped_at": None,
        "is_in_stock": None,
        "dt_is_in_stock_scraped_at": None,
        "stock_count": None,
        "dt_stock_count_scraped_at": None,
        "is_on_promotion": 0,
        "dt_created": None,
        "dt_updated": None,
    }

    # Store
    store_id = current_store.id
    store_product_data['store_id'] = store_id

    # scrape_type
    store_product_data['scrape_type'] = uncleaned_scraped_data.scrape_type

    # Map `price` from `price.value`
    if "price" in raw_product_json:
        store_product_data["price"] = raw_product_json["price"]
    else:
        raise ValueError("price not found in uncleaned_scraped_data")

    # Map `dt_price_scraped_at` from uncleaned product data
    store_product_data["dt_price_scraped_at"] = getattr(uncleaned_scraped_data, "dt_scraped", None)
    if not store_product_data["dt_price_scraped_at"]:
        raise ValueError("dt_scraped not found in uncleaned_scraped_data")

    # Map `is_in_stock` from `stock.stockLevelStatus`
    if "stock" in raw_product_json:
        store_product_data["is_in_stock"] = raw_product_json["stock"] == "1"
    else:
        raise ValueError("stock levels non-1")

    # Map `dt_is_in_stock_scraped_at` (same as `dt_price_scraped_at`)
    if uncleaned_scraped_data.scrape_type == ScrapeType.CATEGORY.value:
        store_product_data["dt_is_in_stock_scraped_at"] = getattr(uncleaned_scraped_data, "dt_scraped", None)

    # Map `dt_stock_count_scraped_at` (same as `dt_price_scraped_at`)
    # store_product_data["dt_stock_count_scraped_at"] = getattr(uncleaned_scraped_data, "dt_scraped", None)

    # Map `is_on_promotion` from `potentialPromotions`
    if "potentialPromotions" in raw_product_json:
        store_product_data["is_on_promotion"] = len(raw_product_json["potentialPromotions"]) > 0

    return store_product_data


def create_or_update_store_product(
    session: sessionmaker, 
    product: Product, 
    store: Store, 
    scrape_type: ScrapeType,
    price: float, 
    is_priced_per_unit: Optional[bool], 
    price_per_unit: Optional[float], 
    dt_price_scraped_at: datetime, 
    is_in_stock: bool,
    dt_is_in_stock_scraped_at:datetime,
    stock_count: Optional[int], 
    dt_stock_count_scraped_at: datetime, 
    is_on_promotion: bool, 
    ) -> Tuple[bool, StoreProduct]:
    """
    Create or update a store product.
    Args:
        session (Session): The SQLAlchemy session.
        product (Product): The product.
        store (Store): The store.
        scrape_type (ScrapeType): The type of scrape.
        price (float): The price of the product.
        is_priced_per_unit (bool): Whether the product is priced per unit.
        price_per_unit (float): The price per unit of the product.
        dt_price_scraped_at (datetime): The date the price was scraped.
        is_in_stock (bool): Whether the product is in stock.
        dt_is_in_stock_scraped_at (datetime): The date that is_in_stock was scraped.
        stock_count (int): The stock count of the product.
        dt_stock_count_scraped_at (datetime): The date the stock count was scraped.
        is_on_promotion (bool): Whether the product is on promotion.
    Returns:
        StoreProduct: The updated or created store product.
    """
    store_product = session.query(StoreProduct).filter_by(product=product, store=store).first()
    created = False
    if store_product is None:
        print(f'Creating store product with product: {product.id} and store: {store}')
        created = True
        store_product = StoreProduct(
            product=product,
            store=store,
            price=price,
            is_priced_per_unit=is_priced_per_unit,
            price_per_unit=price_per_unit,
            dt_price_scraped_at=dt_price_scraped_at,
            is_in_stock=is_in_stock,
            dt_is_in_stock_scraped_at=dt_is_in_stock_scraped_at,
            stock_count=stock_count,
            dt_stock_count_scraped_at=dt_stock_count_scraped_at,
            is_on_promotion=is_on_promotion
        )
        session.add(store_product)
    else:
        print(f'Updating store product with product: {product.id} and store: {store}')
        if scrape_type == ScrapeType.CATEGORY.value:
            store_product.price = price
            store_product.dt_price_scraped_at = dt_price_scraped_at
            store_product.is_in_stock = is_in_stock
            store_product.dt_stock_count_scraped_at = dt_stock_count_scraped_at
            store_product.is_on_promotion = is_on_promotion
            store_product.dt_updated = datetime.now()
        elif scrape_type == ScrapeType.DETAILS.value:
            store_product.price = price
            store_product.is_priced_per_unit = is_priced_per_unit
            store_product.price_per_unit = price_per_unit
            store_product.dt_price_scraped_at = dt_price_scraped_at
            store_product.is_in_stock = is_in_stock
            store_product.stock_count = stock_count
            store_product.dt_stock_count_scraped_at = dt_stock_count_scraped_at
            store_product.is_on_promotion = is_on_promotion
            store_product.dt_updated = datetime.now()
    session.commit()
    return created, store_product


def create_or_update_price_history(
    session: sessionmaker, 
    store_product: StoreProduct, 
    price: float, 
    price_per_unit: Optional[float], 
    dt_price_scraped_at: datetime
    ) -> PriceHistory:
    """
    Create or update a price history.
    Args:
        session (Session): The SQLAlchemy session.
        store_product (StoreProduct): The store product.
        price (float): The price of the product.
        price_per_unit (float): The price per unit of the product.
        dt_price_scraped_at (datetime): The date the price was scraped.
    Returns:
        PriceHistory: The updated or created price history.
    """
    price_history = session.query(PriceHistory).filter_by(
        store_product=store_product,
        price=price,
        price_per_unit=price_per_unit,
        dt_price_scraped_at=dt_price_scraped_at
        ).first()
    if price_history is None:
        price_history = PriceHistory(
            store_product=store_product,
            price=price,
            price_per_unit=price_per_unit,
            dt_price_scraped_at=dt_price_scraped_at
        )
        session.add(price_history)
        session.commit()
    else:
        print(f'Price history already exists for store product: {store_product.id}')
    return price_history

      
def create_or_update_stock_history(
    session: sessionmaker,
    store_product: StoreProduct,
    is_in_stock: bool,
    dt_is_in_stock_scraped_at: datetime,
    stock_count: Optional[int],
    dt_stock_count_scraped_at: datetime
    ) -> StockHistory:
    """
    Create or update a stock history.
    Args:
        session (Session): The SQLAlchemy session.
        store_product (StoreProduct): The store product.
        is_in_stock (bool): Whether the product is in stock.
        dt_is_in_stock_scraped_at (datetime): The date that is_in_stock was scraped.
        stock_count (int): The stock count of the product.
        dt_stock_count_scraped_at (datetime): The date the stock count was scraped.
    Returns:
        StockHistory: The updated or created stock history.
    """
    stock_history = session.query(StockHistory).filter_by(
        store_product=store_product,
        is_in_stock=is_in_stock,
        dt_is_in_stock_scraped_at=dt_is_in_stock_scraped_at,
        stock_count=stock_count,
        dt_stock_count_scraped_at=dt_stock_count_scraped_at
        ).first()
    if stock_history is None:
        stock_history = StockHistory(
            store_product=store_product,
            is_in_stock=is_in_stock,
            dt_is_in_stock_scraped_at=dt_is_in_stock_scraped_at,
            stock_count=stock_count,
            dt_stock_count_scraped_at=dt_stock_count_scraped_at
        )
        session.add(stock_history)
        session.commit()
    else:
        print(f'Stock history already exists for store product: {store_product.id}')
    return stock_history



def clean_and_insert_raw_scraped_data_to_db(
        session: sessionmaker,
        MAIN_RETAILER_URL: str,
        CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES: list,

        # FUNCTIONS
        read_uncleaned_scraped_data: callable,
        checkers_map_scraped_data_to_product: callable,
        create_or_update_product: callable,
        checkers_map_scraped_data_to_store_product: callable,
        create_or_update_store_product: callable,
        create_or_update_price_history: callable,
        create_or_update_stock_history: callable,
        ):
    """
    Cleans and inserts scraped data into the database. Uses mappers to place all the cleaning logic then uses create or updates for the inserts
    """

    retailer = session.query(Retailer).filter_by(url=MAIN_RETAILER_URL).first()
    print(retailer)


    # <ScrapedData>
    uncleaned_scraped_data = read_uncleaned_scraped_data(session=session, retailer=retailer)
    print(uncleaned_scraped_data)
    raw_products_on_page_json:dict = json.loads(uncleaned_scraped_data.data)
    print(raw_products_on_page_json)

    # <Store>
    current_store:Store = uncleaned_scraped_data.store
    # print(current_store)
    for raw_product_json in raw_products_on_page_json:
        print(f"raw_product_json: {raw_product_json}")
        # <Product>
        mapped_product_data = checkers_map_scraped_data_to_product(raw_product_json=raw_product_json, 
                                                          uncleaned_scraped_data=uncleaned_scraped_data,
                                                          CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES=CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES,
                                                          
                                                          checkers_get_is_liquor_attribute_for_mapper=checkers_get_is_liquor_attribute_for_mapper,
                                                          checkers_get_is_liquor_attribute=checkers_get_is_liquor_attribute
                                                          )
        
        was_created, cleaned_product = create_or_update_product(
            session=session,
            sku = mapped_product_data["sku"],
            name = mapped_product_data["name"],
            url = mapped_product_data["url"],
            is_liquor = mapped_product_data["is_liquor"],
            date_scraped = mapped_product_data["date_scraped"],
            thumbnail_image_url = mapped_product_data["thumbnail_image_url"],
            detail_image_url = mapped_product_data["detail_image_url"],
            description = mapped_product_data["description"],
            retailer = mapped_product_data["retailer_id"],
        )
        print(f"create_or_update_product: {was_created, cleaned_product}")


                # <StoreProduct>
        mapped_store_product_data = checkers_map_scraped_data_to_store_product(raw_product_json=raw_product_json, uncleaned_scraped_data=uncleaned_scraped_data,current_store=current_store)
        print(f"mapped_store_product_data: {mapped_store_product_data}")
        was_created, cleaned_store_product = create_or_update_store_product(
            session=session,
            product=cleaned_product,
            store=current_store,
            scrape_type=mapped_store_product_data['scrape_type'],
            price=mapped_store_product_data['price'],
            is_priced_per_unit=mapped_store_product_data['is_priced_per_unit'],
            price_per_unit=mapped_store_product_data['price_per_unit'],
            dt_price_scraped_at=mapped_store_product_data['dt_price_scraped_at'],
            is_in_stock=mapped_store_product_data['is_in_stock'],
            dt_is_in_stock_scraped_at=mapped_store_product_data['dt_is_in_stock_scraped_at'],
            stock_count=mapped_store_product_data['stock_count'],
            dt_stock_count_scraped_at=mapped_store_product_data['dt_stock_count_scraped_at'],
            is_on_promotion=mapped_store_product_data['is_on_promotion'],
        )
        print(f"create_or_update_store_product: {was_created, cleaned_store_product}")

        # <PriceHistory>
        # use the `mapped_store_product_data` data to create a price history
        price_history = create_or_update_price_history(
            session=session,
            store_product=cleaned_store_product,
            price=mapped_store_product_data['price'],
            price_per_unit=mapped_store_product_data['price_per_unit'],
            dt_price_scraped_at=mapped_store_product_data['dt_price_scraped_at']
        )
        print(f"create_or_update_price_history: {price_history}")

        # <StockHistory>
        # use the `mapped_store_product_data` data to create a stock history
        stock_history = create_or_update_stock_history(
            session=session,
            store_product=cleaned_store_product,
            is_in_stock=mapped_store_product_data['is_in_stock'],
            dt_is_in_stock_scraped_at=mapped_store_product_data['dt_is_in_stock_scraped_at'],
            stock_count=mapped_store_product_data['stock_count'],
            dt_stock_count_scraped_at=mapped_store_product_data['dt_stock_count_scraped_at']
        )
        print(f"create_or_update_stock_history: {stock_history}")


        print(f"{'-'*50}")

        # break

    # write to ScrapedData that the data has been cleaned
    uncleaned_scraped_data.is_cleaned = True
    uncleaned_scraped_data.dt_updated = datetime.now()
    session.commit()
    print(f"{'*'*100}")        


#  if there is uncleaned data, clean it
i = 0
while True:
    with Session() as session:
        uncleaned_count = session.query(ScrapedData).filter(ScrapedData.is_cleaned == False).count()
        print(f"uncleaned_count: {uncleaned_count}")
        # every 100 iterations, print the count of uncleaned data
        if uncleaned_count % 100 == 0:
            print(f"uncleaned_count: {uncleaned_count}")
        if uncleaned_count == 0:
            break


        try:
            clean_and_insert_raw_scraped_data_to_db(
                session=session,
                MAIN_RETAILER_URL=MAIN_RETAILER_URL,
                CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES=CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES,
                read_uncleaned_scraped_data=read_uncleaned_scraped_data,
                checkers_map_scraped_data_to_product=checkers_map_scraped_data_to_product,
                create_or_update_product=create_or_update_product,
                checkers_map_scraped_data_to_store_product=checkers_map_scraped_data_to_store_product,
                create_or_update_store_product=create_or_update_store_product,
                create_or_update_price_history=create_or_update_price_history,
                create_or_update_stock_history=create_or_update_stock_history,
            )
        except Exception as e:
            print(f"Error: {e}")
            session.rollback()
            raise e

        i+=1


pd.set_option('display.max_columns', None)

raw_product_json['information.0.price.value']

raw_product_json["information.0.htmlBBs.price_now"]

def checkers_get_is_liquor_attribute_for_mapper(raw_product_json:Dict)->bool:
    liquor_count:int = sum([
            True for CHECKERS_LIQUOR_IDENTIFYING_CATEGORY in CHECKERS_LIQUOR_IDENTIFYING_CATEGORIES 
            if CHECKERS_LIQUOR_IDENTIFYING_CATEGORY in raw_product_json["product_url"]
        ])
    if liquor_count>0:
        return True
    else:
        return False
    
checkers_get_is_liquor_attribute_for_mapper(raw_product_json=raw_product_json)

def checkers_get_product_id_for_mapper(raw_product_json:Dict)-> str:
    return raw_product_json['product_url'].split('/p/')[-1]

checkers_get_product_id_for_mapper(raw_product_json=raw_product_json)

def checkers_get_price_for_mapper(raw_product_json:Dict)-> float:
    # check if `information.0.htmlBBs.price_now` is in the raw_product_json else raise
    if 'information.0.price.value' in raw_product_json:
        return raw_product_json['information.0.price.value']
    else:
        raise ValueError('information.0.price.value not found in raw_product_json')
    
checkers_get_price_for_mapper(raw_product_json=raw_product_json)